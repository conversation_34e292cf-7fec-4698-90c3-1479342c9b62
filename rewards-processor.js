require('dotenv').config();
const Database = require('./src/core/database');
const SolanaTokenChecker = require('./src/core/solana-token-checker');

class RewardsProcessor {
    constructor() {
        this.database = null;
        this.tokenChecker = null;
    }

    async init() {
        console.log('🚀 Initializing Rewards Processor...');
        this.database = new Database();
        await this.database.init();
        await this.createRewardsTables();
        await this.migrateDatabase();

        // Initialize Solana token checker
        this.tokenChecker = new SolanaTokenChecker();
        await this.tokenChecker.init();

        console.log('✅ Rewards Processor initialized');
    }

    async createRewardsTables() {
        // Create rewards tracking tables
        const tables = [
            // Reward distributions table - tracks when rewards were sent
            `CREATE TABLE IF NOT EXISTS reward_distributions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                distribution_timestamp DATETIME NOT NULL,
                total_players INTEGER DEFAULT 0,
                total_viewers INTEGER DEFAULT 0,
                total_amount_distributed REAL DEFAULT 0,
                player_amount REAL DEFAULT 0,
                viewer_amount REAL DEFAULT 0,
                from_timestamp DATETIME NOT NULL,
                to_timestamp DATETIME NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Individual reward records - tracks rewards per user
            `CREATE TABLE IF NOT EXISTS user_rewards (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                distribution_id INTEGER,
                user_id INTEGER,
                username TEXT NOT NULL,
                user_address TEXT,
                reward_type TEXT NOT NULL CHECK (reward_type IN ('player', 'viewer')),
                amount REAL NOT NULL,
                score INTEGER DEFAULT 0,
                token_balance REAL DEFAULT 0,
                balance_amount REAL DEFAULT 0,
                balance_amount_ui REAL DEFAULT 0,
                is_eligible BOOLEAN DEFAULT FALSE,
                has_been_rewarded BOOLEAN DEFAULT FALSE,
                balance_check_error TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (distribution_id) REFERENCES reward_distributions (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )`,

            // User eligibility tracking - tracks token balance eligibility per processing period
            `CREATE TABLE IF NOT EXISTS user_eligibility (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                username TEXT NOT NULL,
                user_address TEXT,
                reward_type TEXT NOT NULL CHECK (reward_type IN ('player', 'viewer')),
                processing_timestamp DATETIME NOT NULL,
                token_balance REAL DEFAULT 0,
                balance_amount REAL DEFAULT 0,
                balance_amount_ui REAL DEFAULT 0,
                minimum_required_balance REAL NOT NULL,
                is_eligible BOOLEAN DEFAULT FALSE,
                has_been_rewarded BOOLEAN DEFAULT FALSE,
                balance_check_error TEXT,
                score INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                UNIQUE(user_id, processing_timestamp, reward_type)
            )`,

            // Last processed timestamp tracking
            `CREATE TABLE IF NOT EXISTS processing_state (
                id INTEGER PRIMARY KEY CHECK (id = 1),
                last_processed_timestamp DATETIME NOT NULL,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`
        ];

        for (const table of tables) {
            await this.database.runQuery(table);
        }

        // Create indexes for optimization
        const indexes = [
            'CREATE INDEX IF NOT EXISTS idx_reward_distributions_timestamp ON reward_distributions (distribution_timestamp)',
            'CREATE INDEX IF NOT EXISTS idx_user_rewards_distribution_id ON user_rewards (distribution_id)',
            'CREATE INDEX IF NOT EXISTS idx_user_rewards_user_id ON user_rewards (user_id)',
            'CREATE INDEX IF NOT EXISTS idx_user_rewards_type ON user_rewards (reward_type)'
        ];

        for (const index of indexes) {
            await this.database.runQuery(index);
        }

        console.log('✅ Rewards tables and indexes created successfully');
    }

    async migrateDatabase() {
        try {
            console.log('🔄 Checking for database migrations...');

            // Check if balance_amount columns exist in user_eligibility table
            const tableInfo = await this.database.allQuery(`PRAGMA table_info(user_eligibility)`);
            const columnNames = tableInfo.map(col => col.name);

            if (!columnNames.includes('balance_amount')) {
                console.log('📝 Adding balance_amount column to user_eligibility table...');
                await this.database.runQuery(`ALTER TABLE user_eligibility ADD COLUMN balance_amount REAL DEFAULT 0`);
            }

            if (!columnNames.includes('balance_amount_ui')) {
                console.log('📝 Adding balance_amount_ui column to user_eligibility table...');
                await this.database.runQuery(`ALTER TABLE user_eligibility ADD COLUMN balance_amount_ui REAL DEFAULT 0`);
            }

            // Check if balance_amount columns exist in user_rewards table
            const rewardsTableInfo = await this.database.allQuery(`PRAGMA table_info(user_rewards)`);
            const rewardsColumnNames = rewardsTableInfo.map(col => col.name);

            if (!rewardsColumnNames.includes('balance_amount')) {
                console.log('📝 Adding balance_amount column to user_rewards table...');
                await this.database.runQuery(`ALTER TABLE user_rewards ADD COLUMN balance_amount REAL DEFAULT 0`);
            }

            if (!rewardsColumnNames.includes('balance_amount_ui')) {
                console.log('📝 Adding balance_amount_ui column to user_rewards table...');
                await this.database.runQuery(`ALTER TABLE user_rewards ADD COLUMN balance_amount_ui REAL DEFAULT 0`);
            }

            console.log('✅ Database migration completed');

        } catch (error) {
            console.error('❌ Database migration failed:', error);
            throw error;
        }
    }

    async getLastProcessedTimestamp() {
        try {
            const result = await this.database.getQuery(
                'SELECT last_processed_timestamp FROM processing_state WHERE id = 1'
            );

            if (result) {
                return new Date(result.last_processed_timestamp);
            } else {
                // If no record exists, start from 24 hours ago
                const defaultTimestamp = new Date(Date.now() - 24 * 60 * 60 * 1000);
                await this.database.runQuery(
                    'INSERT INTO processing_state (id, last_processed_timestamp) VALUES (1, ?)',
                    [defaultTimestamp.toISOString()]
                );
                return defaultTimestamp;
            }
        } catch (error) {
            console.error('Error getting last processed timestamp:', error);
            throw error;
        }
    }

    async updateLastProcessedTimestamp(timestamp) {
        try {
            await this.database.runQuery(
                `INSERT OR REPLACE INTO processing_state (id, last_processed_timestamp, updated_at) 
                 VALUES (1, ?, CURRENT_TIMESTAMP)`,
                [timestamp.toISOString()]
            );
        } catch (error) {
            console.error('Error updating last processed timestamp:', error);
            throw error;
        }
    }

    async getPlayersWithMoves(fromTimestamp) {
        try {
            const sql = `
                SELECT 
                    u.id,
                    u.username,
                    u.user_address,
                    COUNT(cm.id) as total_moves,
                    COUNT(CASE WHEN cm.is_valid_move = 1 THEN 1 END) as valid_moves,
                    MIN(cm.timestamp) as first_move,
                    MAX(cm.timestamp) as last_move
                FROM users u
                INNER JOIN chat_messages cm ON u.id = cm.user_id
                WHERE cm.timestamp > ? AND cm.is_valid_move = 1
                GROUP BY u.id, u.username, u.user_address
                ORDER BY valid_moves DESC, total_moves DESC
            `;

            const players = await this.database.allQuery(sql, [fromTimestamp.toISOString()]);
            console.log(`🎮 Found ${players.length} players with valid moves since ${fromTimestamp.toLocaleString()}`);

            return players;
        } catch (error) {
            console.error('Error getting players with moves:', error);
            throw error;
        }
    }

    async getViewers(fromTimestamp) {
        try {
            const sql = `
                SELECT 
                    u.id,
                    u.username,
                    u.user_address,
                    COUNT(DISTINCT vs.scrape_session_id) as viewing_sessions,
                    MIN(vs.session_timestamp) as first_seen,
                    MAX(vs.session_timestamp) as last_seen,
                    vs.user_role
                FROM users u
                INNER JOIN viewer_sessions vs ON u.id = vs.user_id
                WHERE vs.session_timestamp > ?
                AND user_role = 'viewer'
                GROUP BY u.id, u.username, u.user_address, vs.user_role
                ORDER BY viewing_sessions DESC
            `;

            const allViewers = await this.database.allQuery(sql, [fromTimestamp.toISOString()]);
            console.log(`👥 Found ${allViewers.length} total viewers since ${fromTimestamp.toLocaleString()}`);

            return allViewers;
        } catch (error) {
            console.error('Error getting viewers:', error);
            throw error;
        }
    }

    async removePlayersFromViewers(players, viewers) {
        // Create a Set of player usernames for fast lookup
        const playerUsernames = new Set(players.map(p => p.username));

        // Filter out viewers who are also players
        const viewersOnly = viewers.filter(viewer => !playerUsernames.has(viewer.username));

        console.log(`🔄 Removed ${viewers.length - viewersOnly.length} viewers who are also players`);
        console.log(`👥 Final viewer count: ${viewersOnly.length}`);

        return viewersOnly;
    }

    async checkTokenBalancesAndStoreEligibility(users, rewardType, processingTimestamp) {
        try {
            console.log(`\n💰 Checking token balances for ${users.length} ${rewardType}s...`);

            if (users.length === 0) {
                return [];
            }

            // Check token balances for all users
            const usersWithBalances = await this.tokenChecker.checkMultipleBalances(users);

            // Store eligibility data in database
            await this.storeEligibilityData(usersWithBalances, rewardType, processingTimestamp);

            // Filter to only eligible users
            const eligibleUsers = usersWithBalances.filter(user => user.isEligible);
            const ineligibleUsers = usersWithBalances.filter(user => !user.isEligible);

            console.log(`✅ Token balance check for ${rewardType}s complete:`);
            console.log(`   Total checked: ${usersWithBalances.length}`);
            console.log(`   Eligible: ${eligibleUsers.length}`);
            console.log(`   Ineligible: ${ineligibleUsers.length}`);

            if (ineligibleUsers.length > 0) {
                console.log(`\n❌ Ineligible ${rewardType}s (insufficient token balance):`);
                ineligibleUsers.forEach((user, index) => {
                    const balanceStr = (user.balanceAmountUI || 0).toLocaleString();
                    const requiredStr = process.env.MINIMUM_TOKEN_BALANCE_UI || '50000';
                    console.log(`   ${index + 1}. ${user.username} - Balance: ${balanceStr} (Required: ${requiredStr})`);
                });
            }

            return eligibleUsers;

        } catch (error) {
            console.error(`❌ Error checking token balances for ${rewardType}s:`, error);
            throw error;
        }
    }

    async storeEligibilityData(users, rewardType, processingTimestamp) {
        try {
            const minimumBalance = parseInt(process.env.MINIMUM_TOKEN_BALANCE_UI) || 50000;

            for (const user of users) {
                await this.database.runQuery(`
                    INSERT OR REPLACE INTO user_eligibility (
                        user_id, username, user_address, reward_type, processing_timestamp,
                        token_balance, balance_amount, balance_amount_ui, minimum_required_balance,
                        is_eligible, has_been_rewarded, balance_check_error, score
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `, [
                    user.id,
                    user.username,
                    user.user_address,
                    rewardType,
                    processingTimestamp.toISOString(),
                    user.tokenBalance || 0, // Keep for backward compatibility
                    user.balanceAmount || 0,
                    user.balanceAmountUI || 0,
                    minimumBalance,
                    user.isEligible ? 1 : 0,
                    0, // has_been_rewarded - default to false
                    user.balanceCheckError || null,
                    user.valid_moves || user.viewing_sessions || 0
                ]);
            }

            console.log(`📝 Stored eligibility data for ${users.length} ${rewardType}s`);

        } catch (error) {
            console.error(`❌ Error storing eligibility data for ${rewardType}s:`, error);
            throw error;
        }
    }

    async processRewards() {
        try {
            console.log('\n🎯 Starting Rewards Processing...');
            console.log('='.repeat(50));

            // Get the last processed timestamp
            const fromTimestamp = await this.getLastProcessedTimestamp();
            const toTimestamp = new Date();

            console.log(`📅 Processing period: ${fromTimestamp.toLocaleString()} to ${toTimestamp.toLocaleString()}`);

            // 1. Get all players and score them by their amount of moves
            const players = await this.getPlayersWithMoves(fromTimestamp);

            // 2. Get all viewers and remove usernames that are also players
            const allViewers = await this.getViewers(fromTimestamp);
            const viewers = await this.removePlayersFromViewers(players, allViewers);

            // 3. Check token balances for eligibility
            const eligiblePlayers = await this.checkTokenBalancesAndStoreEligibility(players, 'player', toTimestamp);
            const eligibleViewers = await this.checkTokenBalancesAndStoreEligibility(viewers, 'viewer', toTimestamp);

            // 4. Print these to the screen
            this.printResults(eligiblePlayers, eligibleViewers);

            // Update the last processed timestamp
            await this.updateLastProcessedTimestamp(toTimestamp);

            return {
                players: eligiblePlayers,
                viewers: eligibleViewers,
                allPlayers: players,
                allViewers: viewers,
                fromTimestamp,
                toTimestamp
            };

        } catch (error) {
            console.error('💥 Error processing rewards:', error);
            throw error;
        }
    }

    printResults(players, viewers) {
        const minimumBalance = parseInt(process.env.MINIMUM_TOKEN_BALANCE_UI) || 50000;

        console.log('\n📊 REWARDS PROCESSING RESULTS');
        console.log('='.repeat(50));

        console.log('\n🎮 ELIGIBLE PLAYERS (Token Balance ≥ ' + minimumBalance.toLocaleString() + '):');
        console.log('-'.repeat(50));
        if (players.length === 0) {
            console.log('  No eligible players found with valid moves in this period.');
        } else {
            players.forEach((player, index) => {
                console.log(`  ${index + 1}. ${player.username} ✅`);
                console.log(`     Valid Moves: ${player.valid_moves}`);
                console.log(`     Total Messages: ${player.total_moves}`);
                console.log(`     Address: ${player.user_address || 'N/A'}`);
                console.log(`     Token Balance: ${(player.balanceAmountUI || 0).toLocaleString()}`);
                console.log(`     Period: ${new Date(player.first_move).toLocaleString()} - ${new Date(player.last_move).toLocaleString()}`);
                console.log('');
            });
        }

        console.log('\n👥 ELIGIBLE VIEWERS (Token Balance ≥ ' + minimumBalance.toLocaleString() + '):');
        console.log('-'.repeat(50));
        if (viewers.length === 0) {
            console.log('  No eligible viewers found in this period.');
        } else {
            viewers.forEach((viewer, index) => {
                console.log(`  ${index + 1}. ${viewer.username} (${viewer.user_role}) ✅`);
                console.log(`     Viewing Sessions: ${viewer.viewing_sessions}`);
                console.log(`     Address: ${viewer.user_address || 'N/A'}`);
                console.log(`     Token Balance: ${(viewer.balanceAmountUI || 0).toLocaleString()}`);
                console.log(`     Period: ${new Date(viewer.first_seen).toLocaleString()} - ${new Date(viewer.last_seen).toLocaleString()}`);
                console.log('');
            });
        }

        console.log(`\n📈 ELIGIBILITY SUMMARY:`);
        console.log(`  Eligible Players: ${players.length}`);
        console.log(`  Eligible Viewers: ${viewers.length}`);
        console.log(`  Total Eligible for Rewards: ${players.length + viewers.length}`);
        console.log(`  Minimum Token Balance Required: ${minimumBalance.toLocaleString()}`);
    }

    async close() {
        if (this.tokenChecker) {
            await this.tokenChecker.close();
        }
        if (this.database) {
            await this.database.close();
            console.log('✅ Rewards Processor closed');
        }
    }
}

// CLI interface
async function main() {
    const processor = new RewardsProcessor();

    try {
        await processor.init();
        await processor.processRewards();
    } catch (error) {
        console.error('💥 Fatal error:', error);
    } finally {
        await processor.close();
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = RewardsProcessor;
